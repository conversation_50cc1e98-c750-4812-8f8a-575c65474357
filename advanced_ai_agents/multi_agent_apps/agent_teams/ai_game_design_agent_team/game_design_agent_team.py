import asyncio
import streamlit as st
from autogen import OpenAIWrapper
from autogen.agentchat.contrib.swarm_agent import (
    SwarmAgent,
    initiate_swarm_chat,
    AFTER_WORK,
    AfterWorkOption
)
from autogen.agentchat.group.context_variables import ContextVariables

# Define SwarmResult class since it's not available in the current version
class SwarmResult:
    def __init__(self, agent=None, context_variables=None):
        self.agent = agent
        self.context_variables = context_variables

# Initialize session state
if 'output' not in st.session_state:
    st.session_state.output = {'story': '', 'gameplay': '', 'visuals': '', 'tech': ''}

# Sidebar for API key input
st.sidebar.title("API Key")
api_key = st.sidebar.text_input("Enter your OpenAI API Key", type="password")

# Add guidance in sidebar
st.sidebar.success("""
✨ **Getting Started**

Please provide inputs and features for your dream game! Consider:
- The overall vibe and setting
- Core gameplay elements
- Target audience and platforms
- Visual style preferences
- Technical requirements

The AI agents will collaborate to develop a comprehensive game concept based on your specifications.
""")

# Main app UI
st.title("🎮 AI Game Design Agent Team")

# Add agent information below title
st.info("""
**Meet Your AI Game Design Team:**

🎭 **Story Agent** - Crafts compelling narratives and rich worlds

🎮 **Gameplay Agent** - Creates engaging mechanics and systems

🎨 **Visuals Agent** - Shapes the artistic vision and style

⚙️ **Tech Agent** - Provides technical direction and solutions
                
These agents collaborate to create a comprehensive game concept based on your inputs.
""")

# User inputs
st.subheader("Game Details")
col1, col2 = st.columns(2)

with col1:
    background_vibe = st.text_input("Background Vibe", "Epic fantasy with dragons")
    game_type = st.selectbox("Game Type", ["RPG", "Action", "Adventure", "Puzzle", "Strategy", "Simulation", "Platform", "Horror"])
    target_audience = st.selectbox("Target Audience", ["Kids (7-12)", "Teens (13-17)", "Young Adults (18-25)", "Adults (26+)", "All Ages"])
    player_perspective = st.selectbox("Player Perspective", ["First Person", "Third Person", "Top Down", "Side View", "Isometric"])
    multiplayer = st.selectbox("Multiplayer Support", ["Single Player Only", "Local Co-op", "Online Multiplayer", "Both Local and Online"])

with col2:
    game_goal = st.text_input("Game Goal", "Save the kingdom from eternal winter")
    art_style = st.selectbox("Art Style", ["Realistic", "Cartoon", "Pixel Art", "Stylized", "Low Poly", "Anime", "Hand-drawn"])
    platform = st.multiselect("Target Platforms", ["PC", "Mobile", "PlayStation", "Xbox", "Nintendo Switch", "Web Browser"])
    development_time = st.slider("Development Time (months)", 1, 36, 12)
    cost = st.number_input("Budget (USD)", min_value=0, value=10000, step=5000)

# Additional details
st.subheader("Detailed Preferences")
col3, col4 = st.columns(2)

with col3:
    core_mechanics = st.multiselect(
        "Core Gameplay Mechanics",
        ["Combat", "Exploration", "Puzzle Solving", "Resource Management", "Base Building", "Stealth", "Racing", "Crafting"]
    )
    mood = st.multiselect(
        "Game Mood/Atmosphere",
        ["Epic", "Mysterious", "Peaceful", "Tense", "Humorous", "Dark", "Whimsical", "Scary"]
    )

with col4:
    inspiration = st.text_area("Games for Inspiration (comma-separated)", "")
    unique_features = st.text_area("Unique Features or Requirements", "")

depth = st.selectbox("Level of Detail in Response", ["Low", "Medium", "High"])

# Button to start the agent collaboration
if st.button("Generate Game Concept"):
    # Check if API key is provided
    if not api_key:
        st.error("Please enter your OpenAI API key.")
    else:
        with st.spinner('🤖 AI Agents are collaborating on your game concept...'):
            # Prepare the task based on user inputs
            task = f"""
            Create a game concept with the following details:
            - Background Vibe: {background_vibe}
            - Game Type: {game_type}
            - Game Goal: {game_goal}
            - Target Audience: {target_audience}
            - Player Perspective: {player_perspective}
            - Multiplayer Support: {multiplayer}
            - Art Style: {art_style}
            - Target Platforms: {', '.join(platform)}
            - Development Time: {development_time} months
            - Budget: ${cost:,}
            - Core Mechanics: {', '.join(core_mechanics)}
            - Mood/Atmosphere: {', '.join(mood)}
            - Inspiration: {inspiration}
            - Unique Features: {unique_features}
            - Detail Level: {depth}
            """

            llm_config = {"config_list": [{"model": "gpt-4o-mini","api_key": api_key}]}

            # initialize context variables
            context_variables = ContextVariables({
                "story": None,
                "gameplay": None,
                "visuals": None,
                "tech": None,
            })

            # define functions to be called by the agents
            def update_story_overview(story_summary: str) -> str:
                """Keep the summary as short as possible."""
                context_variables["story"] = story_summary
                st.sidebar.success('Story overview: ' + story_summary)
                return "gameplay_agent"

            def update_gameplay_overview(gameplay_summary: str) -> str:
                """Keep the summary as short as possible."""
                context_variables["gameplay"] = gameplay_summary
                st.sidebar.success('Gameplay overview: ' + gameplay_summary)
                return "visuals_agent"

            def update_visuals_overview(visuals_summary: str) -> str:
                """Keep the summary as short as possible."""
                context_variables["visuals"] = visuals_summary
                st.sidebar.success('Visuals overview: ' + visuals_summary)
                return "tech_agent"

            def update_tech_overview(tech_summary: str) -> str:
                """Keep the summary as short as possible."""
                context_variables["tech"] = tech_summary
                st.sidebar.success('Tech overview: ' + tech_summary)
                return "story_agent"

            system_messages = {
                "story_agent": """
            You are an experienced game story designer specializing in narrative design and world-building. Your task is to:
            1. Create a compelling narrative that aligns with the specified game type and target audience.
            2. Design memorable characters with clear motivations and character arcs.
            3. Develop the game's world, including its history, culture, and key locations.
            4. Plan story progression and major plot points.
            5. Integrate the narrative with the specified mood/atmosphere.
            6. Consider how the story supports the core gameplay mechanics.
                """,
                "gameplay_agent": """
            You are a senior game mechanics designer with expertise in player engagement and systems design. Your task is to:
            1. Design core gameplay loops that match the specified game type and mechanics.
            2. Create progression systems (character development, skills, abilities).
            3. Define player interactions and control schemes for the chosen perspective.
            4. Balance gameplay elements for the target audience.
            5. Design multiplayer interactions if applicable.
            6. Specify game modes and difficulty settings.
            7. Consider the budget and development time constraints.
                """,
                "visuals_agent": """
            You are a creative art director with expertise in game visual and audio design. Your task is to:
            1. Define the visual style guide matching the specified art style.
            2. Design character and environment aesthetics.
            3. Plan visual effects and animations.
            4. Create the audio direction including music style, sound effects, and ambient sound.
            5. Consider technical constraints of chosen platforms.
            6. Align visual elements with the game's mood/atmosphere.
            7. Work within the specified budget constraints.
                """,
                "tech_agent": """
            You are a technical director with extensive game development experience. Your task is to:
            1. Recommend appropriate game engine and development tools.
            2. Define technical requirements for all target platforms.
            3. Plan the development pipeline and asset workflow.
            4. Identify potential technical challenges and solutions.
            5. Estimate resource requirements within the budget.
            6. Consider scalability and performance optimization.
            7. Plan for multiplayer infrastructure if applicable.
                """
            }

            # Create system messages for each agent
            def get_system_message(agent_name: str) -> str:
                base_message = system_messages[agent_name]
                current_gen = agent_name.split("_")[0]

                # Add context from other agents
                context_info = ""
                for k, v in context_variables.items():
                    if v is not None:
                        context_info += f"\n{k.capitalize()} Summary:\n{v}"

                if context_info:
                    base_message += f"\n\nContext from other agents:{context_info}"

                return base_message

            # Define agents
            story_agent = SwarmAgent(
                name="story_agent",
                system_message=get_system_message("story_agent"),
                llm_config=llm_config
            )
            story_agent.register_for_llm(name="update_story_overview", description="Update story overview")(update_story_overview)

            gameplay_agent = SwarmAgent(
                name="gameplay_agent",
                system_message=get_system_message("gameplay_agent"),
                llm_config=llm_config
            )
            gameplay_agent.register_for_llm(name="update_gameplay_overview", description="Update gameplay overview")(update_gameplay_overview)

            visuals_agent = SwarmAgent(
                name="visuals_agent",
                system_message=get_system_message("visuals_agent"),
                llm_config=llm_config
            )
            visuals_agent.register_for_llm(name="update_visuals_overview", description="Update visuals overview")(update_visuals_overview)

            tech_agent = SwarmAgent(
                name="tech_agent",
                system_message=get_system_message("tech_agent"),
                llm_config=llm_config
            )
            tech_agent.register_for_llm(name="update_tech_overview", description="Update tech overview")(update_tech_overview)

            # Register handoffs
            story_agent.register_handoff(gameplay_agent)
            gameplay_agent.register_handoff(visuals_agent)
            visuals_agent.register_handoff(tech_agent)
            tech_agent.register_handoff(story_agent)

            chat_result, updated_context_variables, last_speaker = initiate_swarm_chat(
                initial_agent=story_agent,
                agents=[story_agent, gameplay_agent, visuals_agent, tech_agent],
                user_agent=None,
                messages=task,
                max_rounds=13,
                context_variables=context_variables,
                after_work=AfterWorkOption.TERMINATE
            )

            # Update session state with the individual responses
            # Extract the last messages from each agent type
            chat_history = chat_result.chat_history

            # Initialize outputs
            outputs = {'story': '', 'gameplay': '', 'visuals': '', 'tech': ''}

            # Extract responses from chat history
            for message in reversed(chat_history):
                if message.get('name') == 'story_agent' and not outputs['story']:
                    outputs['story'] = message.get('content', '')
                elif message.get('name') == 'gameplay_agent' and not outputs['gameplay']:
                    outputs['gameplay'] = message.get('content', '')
                elif message.get('name') == 'visuals_agent' and not outputs['visuals']:
                    outputs['visuals'] = message.get('content', '')
                elif message.get('name') == 'tech_agent' and not outputs['tech']:
                    outputs['tech'] = message.get('content', '')

            st.session_state.output = outputs

        # Display success message after completion
        st.success('✨ Game concept generated successfully!')

        # Display the individual outputs in expanders
        with st.expander("Story Design"):
            st.markdown(st.session_state.output['story'])

        with st.expander("Gameplay Mechanics"):
            st.markdown(st.session_state.output['gameplay'])

        with st.expander("Visual and Audio Design"):
            st.markdown(st.session_state.output['visuals'])

        with st.expander("Technical Recommendations"):
            st.markdown(st.session_state.output['tech'])

