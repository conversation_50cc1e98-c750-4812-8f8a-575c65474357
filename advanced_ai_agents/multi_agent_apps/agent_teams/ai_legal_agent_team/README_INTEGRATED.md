# 🏛️ Integrated AI Legal Agent Team

A comprehensive Streamlit application that combines traditional legal document analysis with advanced eFiling data processing. This enhanced version includes multiple AI agents working together to provide thorough legal insights from both document content and filing patterns.

## 🚀 New Features

### Enhanced eFiling Data Processing
- **Automated Data Enrichment**: Fetches original filenames from eFiling portals
- **Robust Error Handling**: Comprehensive retry logic and caching
- **Pattern Analysis**: Identifies trends in filing behavior and document types
- **Data Visualization**: Interactive charts and metrics for filing insights

### Expanded AI Agent Team
- **Legal Researcher**: Enhanced with eFiling expertise and DuckDuckGo search
- **Contract Analyst**: Now analyzes both documents and filing patterns
- **Legal Strategist**: Provides strategic insights based on filing data
- **Filing Data Analyst**: New specialist for eFiling pattern analysis
- **Team Lead**: Coordinates comprehensive analysis across all data sources

### Advanced Analysis Capabilities
- **Document Analysis**: Traditional PDF document review and analysis
- **Filing Analysis**: Pattern recognition and efficiency analysis
- **Data Insights**: Interactive visualizations and metrics
- **Custom Queries**: Flexible analysis combining all data sources

## 📁 Project Structure

```
ai_legal_agent_team/
├── legal_agent_team.py              # Original application
├── enhanced_efile_enricher.py       # Enhanced eFiling data processor
├── integrated_legal_agent_team.py   # New integrated application
├── analysis_functions.py            # Analysis helper functions
├── test_enhanced_efile_enricher.py  # Comprehensive tests
├── config.json                      # Configuration settings
├── sample_test_data.json           # Sample eFiling data
├── requirements.txt                 # Original dependencies
├── requirements_integrated.txt      # Enhanced dependencies
└── README_INTEGRATED.md            # This file
```

## 🛠️ Installation & Setup

### 1. Install Dependencies
```bash
pip install -r requirements_integrated.txt
```

### 2. Configure API Keys
You'll need:
- **OpenAI API Key**: From [OpenAI Platform](https://platform.openai.com)
- **Qdrant API Key & URL**: From [Qdrant Cloud](https://cloud.qdrant.io)

### 3. Run the Application
```bash
# Run the integrated application
streamlit run integrated_legal_agent_team.py

# Or run the original application
streamlit run legal_agent_team.py
```

## 📊 Usage Guide

### Document Analysis
1. **Upload PDF**: Upload legal documents for analysis
2. **Select Analysis Type**: Choose from Contract Review, Legal Research, Risk Assessment, or Compliance Check
3. **Review Results**: Get comprehensive analysis with specific document references

### eFiling Data Analysis
1. **Upload JSON**: Upload eFiling data in JSON format
2. **Process Data**: Automatically enriches data with original filenames
3. **Analyze Patterns**: Review filing trends, document types, and efficiency metrics
4. **View Insights**: Interactive visualizations and detailed statistics

### Custom Queries
- Ask specific questions about your documents or filing patterns
- Combine insights from both document content and filing data
- Get strategic recommendations based on comprehensive analysis

## 🧪 Testing

### Run Enhanced Script Tests
```bash
python -m pytest test_enhanced_efile_enricher.py -v
```

### Test with Sample Data
```bash
python enhanced_efile_enricher.py sample_test_data.json sample_output.json
```

## 📈 Enhanced Features

### eFiling Data Enricher
- **Multiple Parsing Strategies**: Various methods to extract filenames
- **Intelligent Caching**: Reduces redundant web requests
- **Rate Limiting**: Respectful to server resources
- **Comprehensive Logging**: Detailed process tracking
- **Error Recovery**: Robust retry mechanisms

### Data Visualization
- **Filing Status Distribution**: Pie charts showing completion rates
- **Document Type Analysis**: Bar charts of document categories
- **Filing Timeline**: Trend analysis over time
- **Interactive Metrics**: Real-time statistics dashboard

### AI Agent Enhancements
- **Context Awareness**: Agents understand both document and filing context
- **Specialized Roles**: Each agent has specific expertise areas
- **Collaborative Analysis**: Team coordination for comprehensive insights
- **Strategic Recommendations**: Actionable advice based on data patterns

## 🔧 Configuration

### eFiling Enricher Settings
Edit `config.json` to customize:
- Request timeouts and delays
- Retry logic parameters
- Parsing strategies
- Cache settings
- Logging configuration

### Example Configuration
```json
{
    "request_timeout": 15,
    "request_delay": 1.0,
    "max_retries": 3,
    "cache": {
        "max_age_days": 30
    },
    "parsing": {
        "file_extensions": [".pdf", ".doc", ".docx", ".tif"],
        "search_patterns": ["original\\s+file", "file\\s+name"]
    }
}
```

## 📝 Sample eFiling Data Format

```json
[
    {
        "efile_id": "FILING001",
        "efile_overview_url": "https://example.com/filing/FILING001",
        "status": "completed",
        "filing_date": "2024-01-15",
        "case_number": "CV-2024-001",
        "document_type": "Motion"
    }
]
```

## 🚨 Error Handling

The enhanced system includes:
- **Graceful Degradation**: Continues processing even with individual failures
- **Detailed Logging**: Comprehensive error tracking and reporting
- **User Feedback**: Clear status messages and progress indicators
- **Recovery Mechanisms**: Automatic retry and fallback strategies

## 🔒 Security Considerations

- **API Key Protection**: Secure handling of sensitive credentials
- **Rate Limiting**: Prevents overwhelming external services
- **Input Validation**: Comprehensive data validation and sanitization
- **Error Sanitization**: Prevents sensitive information leakage

## 📞 Support & Troubleshooting

### Common Issues
1. **Network Timeouts**: Adjust `request_timeout` in config
2. **Rate Limiting**: Increase `request_delay` between requests
3. **Parsing Failures**: Check HTML structure and update parsing strategies
4. **Memory Issues**: Process large datasets in smaller batches

### Logs Location
- **Application Logs**: `efile_enricher.log`
- **Streamlit Logs**: Console output
- **Cache Directory**: `efile_cache/`

## 🎯 Future Enhancements

- **Machine Learning**: Predictive filing success analysis
- **Advanced Visualizations**: More sophisticated data insights
- **Batch Processing**: Large-scale data processing capabilities
- **API Integration**: Direct integration with court systems
- **Document Classification**: Automated document type detection

## 📄 License

This project is part of the awesome-llm-apps repository. Please refer to the main repository for licensing information.

---

*For questions or support, please refer to the main repository documentation or create an issue.*
