#!/usr/bin/env python3
"""
Analysis functions for the integrated legal agent team.
"""

import streamlit as st
import os
import json
import pandas as pd
from typing import Dict, List, Any

def analyze_document(analysis_type):
    """Analyze uploaded PDF document."""
    if not st.session_state.legal_team:
        st.error("Legal team not initialized")
        return
    
    analysis_configs = {
        "Contract Review": "Review this contract and identify key terms, obligations, and potential issues.",
        "Legal Research": "Research relevant cases and precedents related to this document.",
        "Risk Assessment": "Analyze potential legal risks and liabilities in this document.",
        "Compliance Check": "Check this document for regulatory compliance issues."
    }
    
    query = f"""
    Using the uploaded document as reference:
    
    Primary Analysis Task: {analysis_configs[analysis_type]}
    
    Please search the knowledge base and provide specific references from the document.
    """
    
    with st.spinner(f"Performing {analysis_type}..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(query)
            
            if response.content:
                st.markdown("### Analysis Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error during analysis: {str(e)}")

def analyze_filings(analysis_type):
    """Analyze eFiling data."""
    if not st.session_state.legal_team or not st.session_state.filing_data:
        st.error("Legal team or filing data not available")
        return
    
    # Prepare filing data summary for analysis
    filing_summary = f"""
    eFiling Data Summary:
    - Total filings: {len(st.session_state.filing_data)}
    - Sample filing data: {json.dumps(st.session_state.filing_data[:3], indent=2)}
    """
    
    if st.session_state.enriched_filing_data:
        enriched_count = sum(1 for f in st.session_state.enriched_filing_data if f.get('original_filename'))
        filing_summary += f"\n- Enriched filings: {enriched_count}"
    
    analysis_configs = {
        "Filing Pattern Analysis": "Analyze the filing patterns, identify trends, and provide insights on filing behavior.",
        "Document Type Analysis": "Analyze the distribution of document types and identify any patterns or anomalies.",
        "Filing Efficiency Review": "Review the filing efficiency and identify opportunities for improvement.",
        "Compliance Pattern Check": "Check for compliance patterns and identify any potential issues."
    }
    
    query = f"""
    Based on the following eFiling data:
    
    {filing_summary}
    
    Analysis Task: {analysis_configs[analysis_type]}
    
    Please provide detailed insights and recommendations based on the filing data patterns.
    """
    
    with st.spinner(f"Performing {analysis_type}..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(query)
            
            if response.content:
                st.markdown("### Filing Analysis Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error during filing analysis: {str(e)}")

def display_filing_insights():
    """Display filing data insights and visualizations."""
    if not st.session_state.filing_data:
        return
    
    from integrated_legal_agent_team import analyze_filing_patterns, create_filing_visualizations
    
    # Analyze patterns
    stats = analyze_filing_patterns(st.session_state.filing_data)
    
    if stats:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Filings", stats['total_filings'])
        with col2:
            st.metric("Completed", stats['completed_filings'])
        with col3:
            st.metric("Canceled", stats['canceled_filings'])
        with col4:
            st.metric("Enriched", stats['enriched_filings'])
    
    # Create visualizations
    fig_status, fig_doc_types, fig_timeline = create_filing_visualizations(st.session_state.filing_data)
    
    if fig_status:
        st.plotly_chart(fig_status, use_container_width=True)
    
    if fig_doc_types:
        st.plotly_chart(fig_doc_types, use_container_width=True)
    
    if fig_timeline:
        st.plotly_chart(fig_timeline, use_container_width=True)
    
    # Display raw data table
    if st.checkbox("Show Raw Filing Data"):
        df = pd.DataFrame(st.session_state.filing_data)
        st.dataframe(df, use_container_width=True)

def analyze_custom_query(query):
    """Analyze custom user query."""
    if not st.session_state.legal_team:
        st.error("Legal team not initialized")
        return
    
    # Prepare context
    context_info = []
    
    if st.session_state.knowledge_base:
        context_info.append("- PDF document knowledge base available")
    
    if st.session_state.filing_data:
        context_info.append(f"- eFiling data with {len(st.session_state.filing_data)} entries available")
    
    enhanced_query = f"""
    Available Context:
    {chr(10).join(context_info)}
    
    User Query: {query}
    
    Please provide a comprehensive analysis using all available data sources.
    """
    
    with st.spinner("Processing your query..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(enhanced_query)
            
            if response.content:
                st.markdown("### Query Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error processing query: {str(e)}")
