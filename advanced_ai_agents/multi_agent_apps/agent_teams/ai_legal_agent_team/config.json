{"input_file": "myfilings_extracted.json", "output_file": "myfilings_enriched.json", "cache_dir": "efile_cache", "log_file": "efile_enricher.log", "request_timeout": 15, "request_delay": 1.0, "max_retries": 3, "retry_delay": 2.0, "cookies": {"ASP.NET_SessionId": "your-session-id-here"}, "parsing": {"file_extensions": [".pdf", ".doc", ".docx", ".tif", ".tiff", ".txt", ".rtf"], "search_patterns": ["original\\s+file", "file\\s+name", "document\\s+name", "uploaded\\s+file"], "css_selectors": [".filename", ".file-name", ".document-name", "#filename", "#file-name", "[class*=\"file\"]", "[id*=\"file\"]"]}, "cache": {"max_age_days": 30, "validate_content": true}, "logging": {"level": "INFO", "format": "%(asctime)s - %(levelname)s - %(message)s", "console_output": true}}