#!/usr/bin/env python3
"""
Enhanced eFiling Data Enricher

This script enriches legal filing data by fetching original filenames from eFiling portals.
Features:
- Comprehensive error handling and logging
- Configurable settings
- Rate limiting and retry logic
- Multiple parsing strategies
- Progress tracking
- Caching with validation
"""

import json
import os
import time
import logging
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import re
from urllib.parse import urljoin, urlparse
import hashlib

@dataclass
class Config:
    """Configuration settings for the eFiling enricher."""
    input_file: str = "myfilings_extracted.json"
    output_file: str = "myfilings_enriched.json"
    cache_dir: str = "efile_cache"
    log_file: str = "efile_enricher.log"
    request_timeout: int = 15
    request_delay: float = 1.0  # Seconds between requests
    max_retries: int = 3
    retry_delay: float = 2.0
    cookies: Dict[str, str] = None
    
    def __post_init__(self):
        if self.cookies is None:
            self.cookies = {
                "ASP.NET_SessionId": "your-session-id-here",
                # Add other cookies as needed
            }

class EFileEnricher:
    """Enhanced eFiling data enricher with robust error handling and logging."""
    
    def __init__(self, config: Config):
        self.config = config
        self.setup_logging()
        self.setup_directories()
        self.session = requests.Session()
        self.session.cookies.update(config.cookies)
        
        # Statistics
        self.stats = {
            'processed': 0,
            'updated': 0,
            'errors': 0,
            'cached': 0,
            'skipped': 0
        }
    
    def setup_logging(self):
        """Setup comprehensive logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("eFiling Enricher initialized")
    
    def setup_directories(self):
        """Create necessary directories."""
        Path(self.config.cache_dir).mkdir(exist_ok=True)
        self.logger.info(f"Cache directory: {self.config.cache_dir}")
    
    def load_filings(self) -> List[Dict[str, Any]]:
        """Load and validate filing data from JSON file."""
        try:
            if not Path(self.config.input_file).exists():
                raise FileNotFoundError(f"Input file not found: {self.config.input_file}")
            
            with open(self.config.input_file, 'r', encoding='utf-8') as f:
                filings = json.load(f)
            
            if not isinstance(filings, list):
                raise ValueError("Input file must contain a JSON array")
            
            self.logger.info(f"Loaded {len(filings)} filings from {self.config.input_file}")
            return filings
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in input file: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error loading filings: {e}")
            raise
    
    def save_filings(self, filings: List[Dict[str, Any]]):
        """Save enriched filing data to JSON file."""
        try:
            # Create backup of existing output file
            if Path(self.config.output_file).exists():
                backup_file = f"{self.config.output_file}.backup"
                Path(self.config.output_file).rename(backup_file)
                self.logger.info(f"Created backup: {backup_file}")
            
            with open(self.config.output_file, 'w', encoding='utf-8') as f:
                json.dump(filings, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"Saved enriched data to {self.config.output_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving filings: {e}")
            raise
    
    def get_cache_filename(self, efile_id: str) -> str:
        """Generate cache filename for efile ID."""
        # Use hash to handle special characters in IDs
        safe_id = hashlib.md5(efile_id.encode()).hexdigest()
        return os.path.join(self.config.cache_dir, f"{safe_id}_{efile_id}.html")
    
    def is_cache_valid(self, cache_file: str, max_age_days: int = 30) -> bool:
        """Check if cached file is still valid."""
        try:
            if not Path(cache_file).exists():
                return False
            
            file_age = time.time() - Path(cache_file).stat().st_mtime
            return file_age < (max_age_days * 24 * 3600)
            
        except Exception:
            return False
    
    def fetch_page_with_retry(self, url: str, efile_id: str) -> Optional[str]:
        """Fetch webpage with retry logic and error handling."""
        for attempt in range(self.config.max_retries):
            try:
                self.logger.debug(f"Fetching {efile_id} (attempt {attempt + 1})")
                
                response = self.session.get(
                    url, 
                    timeout=self.config.request_timeout,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                )
                response.raise_for_status()
                
                # Basic content validation
                if len(response.text) < 100:
                    raise ValueError("Response too short, likely an error page")
                
                return response.text
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed for {efile_id} (attempt {attempt + 1}): {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                else:
                    self.logger.error(f"All retry attempts failed for {efile_id}")
                    
            except Exception as e:
                self.logger.error(f"Unexpected error fetching {efile_id}: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                else:
                    break
        
        return None
    
    def parse_original_filename(self, html: str, url: str) -> Optional[str]:
        """Parse original filename using multiple strategies."""
        soup = BeautifulSoup(html, 'html.parser')
        
        # Strategy 1: Look for "Original File" text patterns
        filename = self._parse_strategy_original_file(soup)
        if filename:
            return filename
        
        # Strategy 2: Look for file download links
        filename = self._parse_strategy_download_links(soup)
        if filename:
            return filename
        
        # Strategy 3: Look for table cells with file extensions
        filename = self._parse_strategy_table_cells(soup)
        if filename:
            return filename
        
        # Strategy 4: Look for specific CSS classes or IDs
        filename = self._parse_strategy_css_selectors(soup)
        if filename:
            return filename
        
        return None
    
    def _parse_strategy_original_file(self, soup: BeautifulSoup) -> Optional[str]:
        """Strategy 1: Look for 'Original File' text patterns."""
        patterns = [
            r'original\s+file',
            r'file\s+name',
            r'document\s+name',
            r'uploaded\s+file'
        ]
        
        for pattern in patterns:
            for tag in soup.find_all(string=re.compile(pattern, re.IGNORECASE)):
                parent = tag.find_parent()
                if parent:
                    # Look in next sibling or table cell
                    next_element = parent.find_next(['td', 'span', 'div'])
                    if next_element:
                        text = next_element.get_text(strip=True)
                        if self._is_valid_filename(text):
                            return text
        return None
    
    def _parse_strategy_download_links(self, soup: BeautifulSoup) -> Optional[str]:
        """Strategy 2: Look for download links with file extensions."""
        file_extensions = ['.pdf', '.doc', '.docx', '.tif', '.tiff', '.txt', '.rtf']
        
        for link in soup.find_all('a', href=True):
            href = link['href'].lower()
            if any(ext in href for ext in file_extensions):
                # Try link text first
                text = link.get_text(strip=True)
                if self._is_valid_filename(text):
                    return text
                
                # Try filename from URL
                filename = os.path.basename(href)
                if self._is_valid_filename(filename):
                    return filename
        
        return None
    
    def _parse_strategy_table_cells(self, soup: BeautifulSoup) -> Optional[str]:
        """Strategy 3: Look for table cells containing filenames."""
        file_extensions = ['.pdf', '.doc', '.docx', '.tif', '.tiff', '.txt', '.rtf']
        
        for cell in soup.find_all(['td', 'th']):
            text = cell.get_text(strip=True)
            if any(ext in text.lower() for ext in file_extensions):
                if self._is_valid_filename(text):
                    return text
        
        return None
    
    def _parse_strategy_css_selectors(self, soup: BeautifulSoup) -> Optional[str]:
        """Strategy 4: Look for specific CSS selectors that might contain filenames."""
        selectors = [
            '.filename',
            '.file-name',
            '.document-name',
            '#filename',
            '#file-name',
            '[class*="file"]',
            '[id*="file"]'
        ]
        
        for selector in selectors:
            try:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text(strip=True)
                    if self._is_valid_filename(text):
                        return text
            except Exception:
                continue
        
        return None
    
    def _is_valid_filename(self, text: str) -> bool:
        """Check if text looks like a valid filename."""
        if not text or len(text) < 3 or len(text) > 255:
            return False
        
        # Must contain a file extension
        if '.' not in text:
            return False
        
        # Check for common file extensions
        file_extensions = ['.pdf', '.doc', '.docx', '.tif', '.tiff', '.txt', '.rtf']
        if not any(text.lower().endswith(ext) for ext in file_extensions):
            return False
        
        # Should not contain HTML tags or excessive whitespace
        if '<' in text or '>' in text or '\n' in text or '\t' in text:
            return False
        
        return True

    def get_original_filename(self, entry: Dict[str, Any]) -> Optional[str]:
        """Fetch and parse original filename with comprehensive error handling."""
        efile_id = entry.get("efile_id")
        url = entry.get("efile_overview_url")

        # Validation
        if not efile_id:
            self.logger.warning("Missing efile_id in entry")
            return None

        if not url or not url.startswith("http"):
            self.logger.warning(f"Invalid URL for {efile_id}: {url}")
            return None

        # Skip canceled/incomplete filings
        status = entry.get("status", "").lower()
        if status in ["canceled", "incomplete", "rejected"]:
            self.logger.debug(f"Skipping {efile_id} with status: {status}")
            self.stats['skipped'] += 1
            return None

        cache_file = self.get_cache_filename(efile_id)

        # Try to load from cache first
        html = None
        if self.is_cache_valid(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8', errors='ignore') as f:
                    html = f.read()
                self.logger.debug(f"Loaded {efile_id} from cache")
                self.stats['cached'] += 1
            except Exception as e:
                self.logger.warning(f"Error reading cache for {efile_id}: {e}")

        # Fetch from web if not cached
        if not html:
            html = self.fetch_page_with_retry(url, efile_id)
            if not html:
                self.stats['errors'] += 1
                return None

            # Save to cache
            try:
                with open(cache_file, 'w', encoding='utf-8') as f:
                    f.write(html)
                self.logger.debug(f"Cached {efile_id}")
            except Exception as e:
                self.logger.warning(f"Error caching {efile_id}: {e}")

            # Rate limiting
            time.sleep(self.config.request_delay)

        # Parse filename
        try:
            filename = self.parse_original_filename(html, url)
            if filename:
                self.logger.info(f"Found filename for {efile_id}: {filename}")
                return filename
            else:
                self.logger.warning(f"No filename found for {efile_id}")
                return None
        except Exception as e:
            self.logger.error(f"Error parsing filename for {efile_id}: {e}")
            self.stats['errors'] += 1
            return None

    def process_filings(self, filings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process all filings with progress tracking."""
        total = len(filings)
        self.logger.info(f"Processing {total} filings...")

        for i, entry in enumerate(filings, 1):
            try:
                # Skip if already processed
                if entry.get("original_filename"):
                    self.logger.debug(f"Skipping already processed entry {i}/{total}")
                    continue

                self.logger.info(f"Processing entry {i}/{total}: {entry.get('efile_id', 'unknown')}")

                filename = self.get_original_filename(entry)
                if filename:
                    entry["original_filename"] = filename
                    entry["enriched_timestamp"] = time.time()
                    self.stats['updated'] += 1

                self.stats['processed'] += 1

                # Progress reporting
                if i % 10 == 0 or i == total:
                    self.logger.info(f"Progress: {i}/{total} ({i/total*100:.1f}%)")
                    self.print_stats()

            except Exception as e:
                self.logger.error(f"Error processing entry {i}: {e}")
                self.stats['errors'] += 1
                continue

        return filings

    def print_stats(self):
        """Print processing statistics."""
        self.logger.info(f"Stats - Processed: {self.stats['processed']}, "
                        f"Updated: {self.stats['updated']}, "
                        f"Cached: {self.stats['cached']}, "
                        f"Skipped: {self.stats['skipped']}, "
                        f"Errors: {self.stats['errors']}")

    def run(self):
        """Main execution method."""
        try:
            self.logger.info("Starting eFiling enrichment process")

            # Load data
            filings = self.load_filings()

            # Process filings
            enriched_filings = self.process_filings(filings)

            # Save results
            self.save_filings(enriched_filings)

            # Final statistics
            self.logger.info("Enrichment process completed successfully")
            self.print_stats()

            return True

        except Exception as e:
            self.logger.error(f"Fatal error in enrichment process: {e}")
            return False
        finally:
            self.session.close()

def main():
    """Main entry point."""
    # Configuration
    config = Config()

    # Allow command line overrides
    import sys
    if len(sys.argv) > 1:
        config.input_file = sys.argv[1]
    if len(sys.argv) > 2:
        config.output_file = sys.argv[2]

    # Run enricher
    enricher = EFileEnricher(config)
    success = enricher.run()

    if success:
        print(f"\n✅ Enrichment completed successfully!")
        print(f"📁 Input: {config.input_file}")
        print(f"📁 Output: {config.output_file}")
        print(f"📊 Updated {enricher.stats['updated']} entries")
    else:
        print(f"\n❌ Enrichment failed. Check {config.log_file} for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
