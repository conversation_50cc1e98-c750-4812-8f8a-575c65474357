#!/usr/bin/env python3
"""
Integrated AI Legal Agent Team with eFiling Data Processing

This enhanced version of the legal agent team includes:
- Original PDF document analysis capabilities
- eFiling data processing and enrichment
- Filing pattern analysis
- Comprehensive legal document insights
"""

import streamlit as st
from agno.agent import Agent
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.vectordb.qdrant import Qdrant
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.models.openai import OpenAIChat
from agno.embedder.openai import OpenAIEmbedder
import tempfile
import os
import json
import pandas as pd
from agno.document.chunking.document import DocumentChunking
from enhanced_efile_enricher import EFileEnricher, Config
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Any

def init_session_state():
    """Initialize session state variables"""
    if 'openai_api_key' not in st.session_state:
        st.session_state.openai_api_key = None
    if 'qdrant_api_key' not in st.session_state:
        st.session_state.qdrant_api_key = None
    if 'qdrant_url' not in st.session_state:
        st.session_state.qdrant_url = None
    if 'vector_db' not in st.session_state:
        st.session_state.vector_db = None
    if 'legal_team' not in st.session_state:
        st.session_state.legal_team = None
    if 'knowledge_base' not in st.session_state:
        st.session_state.knowledge_base = None
    if 'processed_files' not in st.session_state:
        st.session_state.processed_files = set()
    if 'filing_data' not in st.session_state:
        st.session_state.filing_data = None
    if 'enriched_filing_data' not in st.session_state:
        st.session_state.enriched_filing_data = None

COLLECTION_NAME = "legal_documents"

def init_qdrant():
    """Initialize Qdrant client with configured settings."""
    if not all([st.session_state.qdrant_api_key, st.session_state.qdrant_url]):
        return None
    try:
        vector_db = Qdrant(
            collection=COLLECTION_NAME,
            url=st.session_state.qdrant_url,
            api_key=st.session_state.qdrant_api_key,
            embedder=OpenAIEmbedder(
                id="text-embedding-3-small", 
                api_key=st.session_state.openai_api_key
            )
        )
        return vector_db
    except Exception as e:
        st.error(f"🔴 Qdrant connection failed: {str(e)}")
        return None

def process_document(uploaded_file, vector_db: Qdrant):
    """Process PDF document and create knowledge base."""
    if not st.session_state.openai_api_key:
        raise ValueError("OpenAI API key not provided")
        
    os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
    
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(uploaded_file.getvalue())
            temp_file_path = temp_file.name
        
        st.info("Loading and processing document...")
        
        knowledge_base = PDFKnowledgeBase(
            path=temp_file_path,
            vector_db=vector_db,
            reader=PDFReader(),
            chunking_strategy=DocumentChunking(
                chunk_size=1000,
                overlap=200
            )
        )
        
        with st.spinner('📤 Loading documents into knowledge base...'):
            try:
                knowledge_base.load(recreate=True, upsert=True)
                st.success("✅ Documents stored successfully!")
            except Exception as e:
                st.error(f"Error loading documents: {str(e)}")
                raise
        
        try:
            os.unlink(temp_file_path)
        except Exception:
            pass
            
        return knowledge_base
            
    except Exception as e:
        st.error(f"Document processing error: {str(e)}")
        raise Exception(f"Error processing document: {str(e)}")

def process_filing_data(uploaded_file):
    """Process eFiling JSON data."""
    try:
        # Save uploaded JSON to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json_data = json.loads(uploaded_file.getvalue().decode('utf-8'))
            json.dump(json_data, temp_file, indent=2)
            temp_input_path = temp_file.name
        
        # Create output path
        temp_output_path = temp_input_path.replace('.json', '_enriched.json')
        
        # Configure enricher
        config = Config(
            input_file=temp_input_path,
            output_file=temp_output_path,
            cache_dir=os.path.join(tempfile.gettempdir(), "efile_cache"),
            log_file=os.path.join(tempfile.gettempdir(), "efile_enricher.log"),
            request_delay=0.5,  # Be respectful to servers
            max_retries=2
        )
        
        # Run enricher
        with st.spinner("🔄 Enriching filing data..."):
            enricher = EFileEnricher(config)
            success = enricher.run()
            
            if success:
                # Load enriched data
                with open(temp_output_path, 'r') as f:
                    enriched_data = json.load(f)
                
                st.success(f"✅ Enriched {enricher.stats['updated']} filing entries!")
                
                # Clean up temp files
                try:
                    os.unlink(temp_input_path)
                    os.unlink(temp_output_path)
                except Exception:
                    pass
                
                return json_data, enriched_data
            else:
                st.error("❌ Failed to enrich filing data")
                return json_data, None
                
    except json.JSONDecodeError:
        st.error("❌ Invalid JSON file format")
        return None, None
    except Exception as e:
        st.error(f"❌ Error processing filing data: {str(e)}")
        return None, None

def analyze_filing_patterns(filing_data: List[Dict[str, Any]]):
    """Analyze patterns in filing data."""
    if not filing_data:
        return None
    
    df = pd.DataFrame(filing_data)
    
    # Basic statistics
    stats = {
        'total_filings': len(df),
        'completed_filings': len(df[df.get('status', '') == 'completed']),
        'canceled_filings': len(df[df.get('status', '') == 'canceled']),
        'enriched_filings': len(df[df.get('original_filename', '').notna()]),
    }
    
    # Document type analysis
    if 'document_type' in df.columns:
        doc_type_counts = df['document_type'].value_counts()
        stats['document_types'] = doc_type_counts.to_dict()
    
    # Filing date analysis
    if 'filing_date' in df.columns:
        df['filing_date'] = pd.to_datetime(df['filing_date'], errors='coerce')
        df['filing_month'] = df['filing_date'].dt.to_period('M')
        monthly_counts = df.groupby('filing_month').size()
        stats['monthly_filings'] = monthly_counts.to_dict()
    
    return stats

def create_filing_visualizations(filing_data: List[Dict[str, Any]]):
    """Create visualizations for filing data."""
    if not filing_data:
        return None, None, None
    
    df = pd.DataFrame(filing_data)
    
    # Status distribution pie chart
    if 'status' in df.columns:
        status_counts = df['status'].value_counts()
        fig_status = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="Filing Status Distribution"
        )
    else:
        fig_status = None
    
    # Document type bar chart
    if 'document_type' in df.columns:
        doc_type_counts = df['document_type'].value_counts()
        fig_doc_types = px.bar(
            x=doc_type_counts.index,
            y=doc_type_counts.values,
            title="Document Types Distribution",
            labels={'x': 'Document Type', 'y': 'Count'}
        )
    else:
        fig_doc_types = None
    
    # Filing timeline
    if 'filing_date' in df.columns:
        df['filing_date'] = pd.to_datetime(df['filing_date'], errors='coerce')
        df_timeline = df.dropna(subset=['filing_date'])
        df_timeline = df_timeline.sort_values('filing_date')
        
        fig_timeline = px.line(
            df_timeline,
            x='filing_date',
            title="Filing Timeline",
            labels={'filing_date': 'Date', 'y': 'Cumulative Filings'}
        )
        fig_timeline.update_traces(y=range(1, len(df_timeline) + 1))
    else:
        fig_timeline = None
    
    return fig_status, fig_doc_types, fig_timeline

def initialize_legal_agents(knowledge_base=None):
    """Initialize the legal agent team."""
    try:
        # Legal Researcher
        legal_researcher = Agent(
            name="Legal Researcher",
            role="Legal research specialist with eFiling expertise",
            model=OpenAIChat(id="gpt-4o"),
            tools=[DuckDuckGoTools()],
            knowledge=knowledge_base,
            search_knowledge=True if knowledge_base else False,
            instructions=[
                "Find and cite relevant legal cases and precedents",
                "Provide detailed research summaries with sources",
                "Analyze filing patterns and trends when provided with eFiling data",
                "Reference specific sections from uploaded documents",
                "Always search the knowledge base for relevant information when available"
            ],
            show_tool_calls=True,
            markdown=True
        )

        # Contract Analyst
        contract_analyst = Agent(
            name="Contract Analyst",
            role="Contract and document analysis specialist",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=knowledge_base,
            search_knowledge=True if knowledge_base else False,
            instructions=[
                "Review contracts and legal documents thoroughly",
                "Identify key terms and potential issues",
                "Analyze document filing patterns and compliance",
                "Reference specific clauses from documents",
                "Provide insights on document organization and filing strategies"
            ],
            markdown=True
        )

        # Legal Strategist
        legal_strategist = Agent(
            name="Legal Strategist", 
            role="Legal strategy and filing pattern specialist",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=knowledge_base,
            search_knowledge=True if knowledge_base else False,
            instructions=[
                "Develop comprehensive legal strategies",
                "Provide actionable recommendations",
                "Analyze filing patterns for strategic insights",
                "Consider both risks and opportunities",
                "Recommend optimal filing strategies based on data patterns"
            ],
            markdown=True
        )

        # Filing Data Analyst (New specialist for eFiling analysis)
        filing_analyst = Agent(
            name="Filing Data Analyst",
            role="eFiling data analysis specialist",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=knowledge_base,
            search_knowledge=True if knowledge_base else False,
            instructions=[
                "Analyze eFiling data patterns and trends",
                "Identify filing efficiency opportunities",
                "Provide insights on document management strategies",
                "Analyze filing success rates and common issues",
                "Recommend process improvements based on filing data"
            ],
            markdown=True
        )

        # Legal Agent Team Lead
        legal_team = Agent(
            name="Legal Team Lead",
            role="Legal team coordinator with comprehensive analysis capabilities",
            model=OpenAIChat(id="gpt-4o"),
            team=[legal_researcher, contract_analyst, legal_strategist, filing_analyst],
            knowledge=knowledge_base,
            search_knowledge=True if knowledge_base else False,
            instructions=[
                "Coordinate analysis between team members",
                "Provide comprehensive responses combining document and filing analysis",
                "Ensure all recommendations are properly sourced",
                "Reference specific parts of uploaded documents and filing data",
                "Always search the knowledge base before delegating tasks",
                "Integrate insights from both document content and filing patterns"
            ],
            show_tool_calls=True,
            markdown=True
        )
        
        return legal_team
        
    except Exception as e:
        st.error(f"Error initializing legal agents: {str(e)}")
        return None

def main():
    st.set_page_config(page_title="Integrated Legal Document Analyzer", layout="wide")
    init_session_state()

    st.title("🏛️ Integrated AI Legal Agent Team")
    st.markdown("*Advanced legal document analysis with eFiling data processing*")

    with st.sidebar:
        st.header("🔑 API Configuration")

        openai_key = st.text_input(
            "OpenAI API Key",
            type="password",
            value=st.session_state.openai_api_key if st.session_state.openai_api_key else "",
            help="Enter your OpenAI API key"
        )
        if openai_key:
            st.session_state.openai_api_key = openai_key

        qdrant_key = st.text_input(
            "Qdrant API Key",
            type="password",
            value=st.session_state.qdrant_api_key if st.session_state.qdrant_api_key else "",
            help="Enter your Qdrant API key"
        )
        if qdrant_key:
            st.session_state.qdrant_api_key = qdrant_key

        qdrant_url = st.text_input(
            "Qdrant URL",
            value=st.session_state.qdrant_url if st.session_state.qdrant_url else "",
            help="Enter your Qdrant instance URL"
        )
        if qdrant_url:
            st.session_state.qdrant_url = qdrant_url

        if all([st.session_state.qdrant_api_key, st.session_state.qdrant_url]):
            try:
                if not st.session_state.vector_db:
                    st.session_state.vector_db = init_qdrant()
                    if st.session_state.vector_db:
                        st.success("Successfully connected to Qdrant!")
            except Exception as e:
                st.error(f"Failed to connect to Qdrant: {str(e)}")

        st.divider()

        # Document Upload Section
        if st.session_state.openai_api_key:
            st.header("📄 Document Upload")

            # PDF Document Upload
            uploaded_pdf = st.file_uploader("Upload Legal Document (PDF)", type=['pdf'])

            if uploaded_pdf and st.session_state.vector_db:
                if uploaded_pdf.name not in st.session_state.processed_files:
                    with st.spinner("Processing PDF document..."):
                        try:
                            knowledge_base = process_document(uploaded_pdf, st.session_state.vector_db)
                            if knowledge_base:
                                st.session_state.knowledge_base = knowledge_base
                                st.session_state.processed_files.add(uploaded_pdf.name)
                                st.success("✅ PDF document processed!")
                        except Exception as e:
                            st.error(f"Error processing PDF: {str(e)}")
                else:
                    st.success("✅ PDF document already processed!")

            st.divider()

            # eFiling Data Upload
            st.header("📊 eFiling Data Upload")
            uploaded_json = st.file_uploader("Upload eFiling Data (JSON)", type=['json'])

            if uploaded_json:
                if st.button("Process eFiling Data"):
                    filing_data, enriched_data = process_filing_data(uploaded_json)
                    if filing_data:
                        st.session_state.filing_data = filing_data
                        st.session_state.enriched_filing_data = enriched_data
                        st.success("✅ eFiling data processed!")

            # Initialize agents when we have data
            if (st.session_state.knowledge_base or st.session_state.filing_data) and not st.session_state.legal_team:
                with st.spinner("Initializing legal agent team..."):
                    st.session_state.legal_team = initialize_legal_agents(st.session_state.knowledge_base)
                    if st.session_state.legal_team:
                        st.success("✅ Legal agent team initialized!")

        else:
            st.warning("Please configure OpenAI API key to proceed")

    # Main content area
    if not st.session_state.openai_api_key:
        st.info("👈 Please configure your OpenAI API key in the sidebar to begin")
    elif not (st.session_state.knowledge_base or st.session_state.filing_data):
        st.info("👈 Please upload a legal document (PDF) or eFiling data (JSON) to begin analysis")
    else:
        # Create tabs for different analysis types
        tab1, tab2, tab3, tab4 = st.tabs(["📑 Document Analysis", "📊 Filing Analysis", "📈 Data Insights", "🤖 Custom Query"])

        with tab1:
            st.header("📑 Document Analysis")
            if st.session_state.knowledge_base:
                analysis_type = st.selectbox(
                    "Select Document Analysis Type",
                    [
                        "Contract Review",
                        "Legal Research",
                        "Risk Assessment",
                        "Compliance Check"
                    ]
                )

                if st.button("Analyze Document", key="doc_analyze"):
                    analyze_document(analysis_type)
            else:
                st.info("Upload a PDF document to enable document analysis")

        with tab2:
            st.header("📊 Filing Analysis")
            if st.session_state.filing_data:
                filing_analysis_type = st.selectbox(
                    "Select Filing Analysis Type",
                    [
                        "Filing Pattern Analysis",
                        "Document Type Analysis",
                        "Filing Efficiency Review",
                        "Compliance Pattern Check"
                    ]
                )

                if st.button("Analyze Filings", key="filing_analyze"):
                    analyze_filings(filing_analysis_type)
            else:
                st.info("Upload eFiling JSON data to enable filing analysis")

        with tab3:
            st.header("📈 Data Insights & Visualizations")
            if st.session_state.filing_data:
                display_filing_insights()
            else:
                st.info("Upload eFiling JSON data to view insights")

        with tab4:
            st.header("🤖 Custom Query")
            if st.session_state.legal_team:
                custom_query = st.text_area(
                    "Enter your custom legal query:",
                    help="Ask questions about your documents, filing patterns, or request specific analysis"
                )

                if st.button("Submit Query", key="custom_query") and custom_query:
                    analyze_custom_query(custom_query)
            else:
                st.info("Upload documents or filing data to enable custom queries")

# Import analysis functions
from analysis_functions import analyze_document, analyze_filings, display_filing_insights, analyze_custom_query

if __name__ == "__main__":
    main()
