#!/usr/bin/env python3
"""
AI Legal Agent Team - No Qdrant Version

This version works without Qdrant vector database, using in-memory document processing
for immediate testing and demonstration purposes.
"""

import streamlit as st
from agno.agent import Agent
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.models.openai import OpenAIChat
import tempfile
import os
import json
import pandas as pd
from pathlib import Path
import plotly.express as px
from datetime import datetime
from typing import Dict, List, Any
import PyPDF2
import io

def init_session_state():
    """Initialize session state variables"""
    if 'openai_api_key' not in st.session_state:
        st.session_state.openai_api_key = None
    if 'legal_team' not in st.session_state:
        st.session_state.legal_team = None
    if 'document_text' not in st.session_state:
        st.session_state.document_text = None
    if 'processed_files' not in st.session_state:
        st.session_state.processed_files = set()
    if 'filing_data' not in st.session_state:
        st.session_state.filing_data = None

def extract_pdf_text(uploaded_file):
    """Extract text from uploaded PDF file."""
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(uploaded_file.getvalue()))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
        return text
    except Exception as e:
        st.error(f"Error extracting PDF text: {str(e)}")
        return None

def process_filing_data(uploaded_file):
    """Process eFiling JSON data."""
    try:
        json_data = json.loads(uploaded_file.getvalue().decode('utf-8'))
        return json_data
    except json.JSONDecodeError:
        st.error("❌ Invalid JSON file format")
        return None
    except Exception as e:
        st.error(f"❌ Error processing filing data: {str(e)}")
        return None

def analyze_filing_patterns(filing_data: List[Dict[str, Any]]):
    """Analyze patterns in filing data."""
    if not filing_data:
        return None
    
    df = pd.DataFrame(filing_data)
    
    stats = {
        'total_filings': len(df),
        'completed_filings': len(df[df.get('status', '') == 'completed']) if 'status' in df.columns else 0,
        'canceled_filings': len(df[df.get('status', '') == 'canceled']) if 'status' in df.columns else 0,
    }
    
    if 'document_type' in df.columns:
        doc_type_counts = df['document_type'].value_counts()
        stats['document_types'] = doc_type_counts.to_dict()
    
    return stats

def create_filing_visualizations(filing_data: List[Dict[str, Any]]):
    """Create visualizations for filing data."""
    if not filing_data:
        return None, None, None
    
    df = pd.DataFrame(filing_data)
    
    # Status distribution pie chart
    fig_status = None
    if 'status' in df.columns:
        status_counts = df['status'].value_counts()
        fig_status = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="Filing Status Distribution"
        )
    
    # Document type bar chart
    fig_doc_types = None
    if 'document_type' in df.columns:
        doc_type_counts = df['document_type'].value_counts()
        fig_doc_types = px.bar(
            x=doc_type_counts.index,
            y=doc_type_counts.values,
            title="Document Types Distribution",
            labels={'x': 'Document Type', 'y': 'Count'}
        )
    
    # Filing timeline
    fig_timeline = None
    if 'filing_date' in df.columns:
        df['filing_date'] = pd.to_datetime(df['filing_date'], errors='coerce')
        df_timeline = df.dropna(subset=['filing_date'])
        if not df_timeline.empty:
            df_timeline = df_timeline.sort_values('filing_date')
            fig_timeline = px.line(
                df_timeline,
                x='filing_date',
                title="Filing Timeline",
                labels={'filing_date': 'Date', 'y': 'Cumulative Filings'}
            )
            fig_timeline.update_traces(y=range(1, len(df_timeline) + 1))
    
    return fig_status, fig_doc_types, fig_timeline

def initialize_legal_agents(document_text=None):
    """Initialize the legal agent team."""
    try:
        # Prepare document context if available
        doc_context = ""
        if document_text:
            # Truncate document text to avoid token limits
            doc_context = f"\n\nDocument Content (excerpt):\n{document_text[:3000]}..."
        
        # Legal Researcher
        legal_researcher = Agent(
            name="Legal Researcher",
            role="Legal research specialist with eFiling expertise",
            model=OpenAIChat(id="gpt-4o"),
            tools=[DuckDuckGoTools()],
            instructions=[
                "Find and cite relevant legal cases and precedents",
                "Provide detailed research summaries with sources",
                "Analyze filing patterns and trends when provided with eFiling data",
                "Reference specific sections from provided document content",
                f"Use this document context when available: {doc_context}"
            ],
            show_tool_calls=True,
            markdown=True
        )

        # Contract Analyst
        contract_analyst = Agent(
            name="Contract Analyst",
            role="Contract and document analysis specialist",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "Review contracts and legal documents thoroughly",
                "Identify key terms and potential issues",
                "Analyze document filing patterns and compliance",
                "Reference specific clauses from documents",
                f"Use this document context when available: {doc_context}"
            ],
            markdown=True
        )

        # Legal Strategist
        legal_strategist = Agent(
            name="Legal Strategist", 
            role="Legal strategy and filing pattern specialist",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "Develop comprehensive legal strategies",
                "Provide actionable recommendations",
                "Analyze filing patterns for strategic insights",
                "Consider both risks and opportunities",
                f"Use this document context when available: {doc_context}"
            ],
            markdown=True
        )

        # Filing Data Analyst
        filing_analyst = Agent(
            name="Filing Data Analyst",
            role="eFiling data analysis specialist",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "Analyze eFiling data patterns and trends",
                "Identify filing efficiency opportunities",
                "Provide insights on document management strategies",
                "Analyze filing success rates and common issues",
                "Recommend process improvements based on filing data"
            ],
            markdown=True
        )

        # Legal Agent Team Lead
        legal_team = Agent(
            name="Legal Team Lead",
            role="Legal team coordinator with comprehensive analysis capabilities",
            model=OpenAIChat(id="gpt-4o"),
            team=[legal_researcher, contract_analyst, legal_strategist, filing_analyst],
            instructions=[
                "Coordinate analysis between team members",
                "Provide comprehensive responses combining document and filing analysis",
                "Ensure all recommendations are properly sourced",
                "Reference specific parts of provided documents and filing data",
                "Integrate insights from both document content and filing patterns",
                f"Use this document context when available: {doc_context}"
            ],
            show_tool_calls=True,
            markdown=True
        )
        
        return legal_team
        
    except Exception as e:
        st.error(f"Error initializing legal agents: {str(e)}")
        return None

def analyze_document(analysis_type):
    """Analyze uploaded PDF document."""
    if not st.session_state.legal_team:
        st.error("Legal team not initialized")
        return
    
    if not st.session_state.document_text:
        st.error("No document text available")
        return
    
    analysis_configs = {
        "Contract Review": "Review this contract and identify key terms, obligations, and potential issues.",
        "Legal Research": "Research relevant cases and precedents related to this document.",
        "Risk Assessment": "Analyze potential legal risks and liabilities in this document.",
        "Compliance Check": "Check this document for regulatory compliance issues."
    }
    
    query = f"""
    Based on the provided document content:
    
    Primary Analysis Task: {analysis_configs[analysis_type]}
    
    Please provide specific references from the document content and comprehensive analysis.
    """
    
    with st.spinner(f"Performing {analysis_type}..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(query)
            
            if response.content:
                st.markdown("### Analysis Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error during analysis: {str(e)}")

def analyze_filings(analysis_type):
    """Analyze eFiling data."""
    if not st.session_state.legal_team or not st.session_state.filing_data:
        st.error("Legal team or filing data not available")
        return
    
    filing_summary = f"""
    eFiling Data Summary:
    - Total filings: {len(st.session_state.filing_data)}
    - Sample filing data: {json.dumps(st.session_state.filing_data[:3], indent=2)}
    """
    
    analysis_configs = {
        "Filing Pattern Analysis": "Analyze the filing patterns, identify trends, and provide insights on filing behavior.",
        "Document Type Analysis": "Analyze the distribution of document types and identify any patterns or anomalies.",
        "Filing Efficiency Review": "Review the filing efficiency and identify opportunities for improvement.",
        "Compliance Pattern Check": "Check for compliance patterns and identify any potential issues."
    }
    
    query = f"""
    Based on the following eFiling data:
    
    {filing_summary}
    
    Analysis Task: {analysis_configs[analysis_type]}
    
    Please provide detailed insights and recommendations based on the filing data patterns.
    """
    
    with st.spinner(f"Performing {analysis_type}..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(query)
            
            if response.content:
                st.markdown("### Filing Analysis Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error during filing analysis: {str(e)}")

def analyze_custom_query(query):
    """Analyze custom user query."""
    if not st.session_state.legal_team:
        st.error("Legal team not initialized")
        return
    
    context_info = []
    
    if st.session_state.document_text:
        context_info.append("- PDF document content available")
    
    if st.session_state.filing_data:
        context_info.append(f"- eFiling data with {len(st.session_state.filing_data)} entries available")
    
    enhanced_query = f"""
    Available Context:
    {chr(10).join(context_info)}
    
    User Query: {query}
    
    Please provide a comprehensive analysis using all available data sources.
    """
    
    with st.spinner("Processing your query..."):
        try:
            os.environ['OPENAI_API_KEY'] = st.session_state.openai_api_key
            response = st.session_state.legal_team.run(enhanced_query)
            
            if response.content:
                st.markdown("### Query Results")
                st.markdown(response.content)
            else:
                for message in response.messages:
                    if message.role == 'assistant' and message.content:
                        st.markdown(message.content)
                        
        except Exception as e:
            st.error(f"Error processing query: {str(e)}")

def main():
    st.set_page_config(page_title="AI Legal Agent Team (No Qdrant)", layout="wide")
    init_session_state()

    st.title("🏛️ AI Legal Agent Team")
    st.markdown("*Legal document analysis without vector database dependency*")

    # Add info about this version
    st.info("ℹ️ This version works without Qdrant vector database for immediate testing. Document content is processed in-memory.")

    with st.sidebar:
        st.header("🔑 API Configuration")

        openai_key = st.text_input(
            "OpenAI API Key",
            type="password",
            value=st.session_state.openai_api_key if st.session_state.openai_api_key else "",
            help="Enter your OpenAI API key"
        )
        if openai_key:
            st.session_state.openai_api_key = openai_key

        st.divider()

        # Document Upload Section
        if st.session_state.openai_api_key:
            st.header("📄 Document Upload")

            # PDF Document Upload
            uploaded_pdf = st.file_uploader("Upload Legal Document (PDF)", type=['pdf'])

            if uploaded_pdf:
                if uploaded_pdf.name not in st.session_state.processed_files:
                    with st.spinner("Processing PDF document..."):
                        try:
                            document_text = extract_pdf_text(uploaded_pdf)
                            if document_text:
                                st.session_state.document_text = document_text
                                st.session_state.processed_files.add(uploaded_pdf.name)
                                st.success("✅ PDF document processed!")

                                # Show document preview
                                with st.expander("📖 Document Preview"):
                                    st.text_area("Document Content (first 1000 characters):",
                                               value=document_text[:1000] + "..." if len(document_text) > 1000 else document_text,
                                               height=200, disabled=True)
                        except Exception as e:
                            st.error(f"Error processing PDF: {str(e)}")
                else:
                    st.success("✅ PDF document already processed!")

            st.divider()

            # eFiling Data Upload
            st.header("📊 eFiling Data Upload")
            uploaded_json = st.file_uploader("Upload eFiling Data (JSON)", type=['json'])

            if uploaded_json:
                if st.button("Process eFiling Data"):
                    filing_data = process_filing_data(uploaded_json)
                    if filing_data:
                        st.session_state.filing_data = filing_data
                        st.success("✅ eFiling data processed!")

                        # Show data preview
                        with st.expander("📊 Filing Data Preview"):
                            st.json(filing_data[:3] if len(filing_data) > 3 else filing_data)

            # Initialize agents when we have data
            if (st.session_state.document_text or st.session_state.filing_data) and not st.session_state.legal_team:
                with st.spinner("Initializing legal agent team..."):
                    st.session_state.legal_team = initialize_legal_agents(st.session_state.document_text)
                    if st.session_state.legal_team:
                        st.success("✅ Legal agent team initialized!")

        else:
            st.warning("Please configure OpenAI API key to proceed")

    # Main content area
    if not st.session_state.openai_api_key:
        st.info("👈 Please configure your OpenAI API key in the sidebar to begin")
    elif not (st.session_state.document_text or st.session_state.filing_data):
        st.info("👈 Please upload a legal document (PDF) or eFiling data (JSON) to begin analysis")
    else:
        # Create tabs for different analysis types
        tab1, tab2, tab3, tab4 = st.tabs(["📑 Document Analysis", "📊 Filing Analysis", "📈 Data Insights", "🤖 Custom Query"])

        with tab1:
            st.header("📑 Document Analysis")
            if st.session_state.document_text:
                analysis_type = st.selectbox(
                    "Select Document Analysis Type",
                    [
                        "Contract Review",
                        "Legal Research",
                        "Risk Assessment",
                        "Compliance Check"
                    ]
                )

                if st.button("Analyze Document", key="doc_analyze"):
                    analyze_document(analysis_type)
            else:
                st.info("Upload a PDF document to enable document analysis")

        with tab2:
            st.header("📊 Filing Analysis")
            if st.session_state.filing_data:
                filing_analysis_type = st.selectbox(
                    "Select Filing Analysis Type",
                    [
                        "Filing Pattern Analysis",
                        "Document Type Analysis",
                        "Filing Efficiency Review",
                        "Compliance Pattern Check"
                    ]
                )

                if st.button("Analyze Filings", key="filing_analyze"):
                    analyze_filings(filing_analysis_type)
            else:
                st.info("Upload eFiling JSON data to enable filing analysis")

        with tab3:
            st.header("📈 Data Insights & Visualizations")
            if st.session_state.filing_data:
                # Display statistics
                stats = analyze_filing_patterns(st.session_state.filing_data)

                if stats:
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Total Filings", stats['total_filings'])
                    with col2:
                        st.metric("Completed", stats['completed_filings'])
                    with col3:
                        st.metric("Canceled", stats['canceled_filings'])

                # Create visualizations
                fig_status, fig_doc_types, fig_timeline = create_filing_visualizations(st.session_state.filing_data)

                if fig_status:
                    st.plotly_chart(fig_status, use_container_width=True)

                if fig_doc_types:
                    st.plotly_chart(fig_doc_types, use_container_width=True)

                if fig_timeline:
                    st.plotly_chart(fig_timeline, use_container_width=True)

                # Display raw data table
                if st.checkbox("Show Raw Filing Data"):
                    df = pd.DataFrame(st.session_state.filing_data)
                    st.dataframe(df, use_container_width=True)
            else:
                st.info("Upload eFiling JSON data to view insights")

        with tab4:
            st.header("🤖 Custom Query")
            if st.session_state.legal_team:
                custom_query = st.text_area(
                    "Enter your custom legal query:",
                    help="Ask questions about your documents, filing patterns, or request specific analysis"
                )

                if st.button("Submit Query", key="custom_query") and custom_query:
                    analyze_custom_query(custom_query)
            else:
                st.info("Upload documents or filing data to enable custom queries")

if __name__ == "__main__":
    main()
