#!/usr/bin/env python3
"""
Comprehensive tests for the enhanced eFiling enricher.
"""

import unittest
import json
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import time

# Import the enhanced enricher
from enhanced_efile_enricher import <PERSON><PERSON>ileEnricher, Config

class TestEFileEnricher(unittest.TestCase):
    """Test cases for the enhanced eFiling enricher."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config = Config(
            input_file=os.path.join(self.test_dir, "test_input.json"),
            output_file=os.path.join(self.test_dir, "test_output.json"),
            cache_dir=os.path.join(self.test_dir, "cache"),
            log_file=os.path.join(self.test_dir, "test.log"),
            request_delay=0.1,  # Faster for testing
            max_retries=2
        )
        
        # Sample test data
        self.sample_filings = [
            {
                "efile_id": "TEST001",
                "efile_overview_url": "https://example.com/filing/TEST001",
                "status": "completed"
            },
            {
                "efile_id": "TEST002",
                "efile_overview_url": "https://example.com/filing/TEST002",
                "status": "completed",
                "original_filename": "already_processed.pdf"
            },
            {
                "efile_id": "TEST003",
                "efile_overview_url": "https://example.com/filing/TEST003",
                "status": "canceled"
            },
            {
                "efile_id": "TEST004",
                "efile_overview_url": "invalid-url",
                "status": "completed"
            }
        ]
        
        # Sample HTML responses
        self.sample_html_responses = {
            "TEST001": """
            <html>
                <body>
                    <table>
                        <tr><td>Original File:</td><td>contract_agreement.pdf</td></tr>
                    </table>
                </body>
            </html>
            """,
            "TEST002": """
            <html>
                <body>
                    <div class="filename">legal_document.docx</div>
                </body>
            </html>
            """,
            "TEST003": """
            <html>
                <body>
                    <a href="/download/filing.pdf">Download Filing</a>
                </body>
            </html>
            """
        }
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def create_test_input_file(self, filings=None):
        """Create test input JSON file."""
        if filings is None:
            filings = self.sample_filings
        
        with open(self.config.input_file, 'w') as f:
            json.dump(filings, f)
    
    def test_initialization(self):
        """Test enricher initialization."""
        enricher = EFileEnricher(self.config)
        
        # Check directories are created
        self.assertTrue(Path(self.config.cache_dir).exists())
        
        # Check logger is set up
        self.assertIsNotNone(enricher.logger)
        
        # Check session is created
        self.assertIsNotNone(enricher.session)
    
    def test_load_filings_success(self):
        """Test successful loading of filings."""
        self.create_test_input_file()
        enricher = EFileEnricher(self.config)
        
        filings = enricher.load_filings()
        
        self.assertEqual(len(filings), 4)
        self.assertEqual(filings[0]["efile_id"], "TEST001")
    
    def test_load_filings_file_not_found(self):
        """Test loading filings when file doesn't exist."""
        enricher = EFileEnricher(self.config)
        
        with self.assertRaises(FileNotFoundError):
            enricher.load_filings()
    
    def test_load_filings_invalid_json(self):
        """Test loading filings with invalid JSON."""
        with open(self.config.input_file, 'w') as f:
            f.write("invalid json content")
        
        enricher = EFileEnricher(self.config)
        
        with self.assertRaises(json.JSONDecodeError):
            enricher.load_filings()
    
    def test_cache_filename_generation(self):
        """Test cache filename generation."""
        enricher = EFileEnricher(self.config)
        
        filename = enricher.get_cache_filename("TEST001")
        
        self.assertTrue(filename.endswith("_TEST001.html"))
        self.assertTrue(filename.startswith(self.config.cache_dir))
    
    def test_cache_validation(self):
        """Test cache validation logic."""
        enricher = EFileEnricher(self.config)
        
        # Create a test cache file
        cache_file = os.path.join(self.config.cache_dir, "test_cache.html")
        with open(cache_file, 'w') as f:
            f.write("<html>test</html>")
        
        # Should be valid (recent file)
        self.assertTrue(enricher.is_cache_valid(cache_file))
        
        # Should be invalid (non-existent file)
        self.assertFalse(enricher.is_cache_valid("nonexistent.html"))
    
    def test_filename_validation(self):
        """Test filename validation logic."""
        enricher = EFileEnricher(self.config)
        
        # Valid filenames
        self.assertTrue(enricher._is_valid_filename("document.pdf"))
        self.assertTrue(enricher._is_valid_filename("contract_agreement.docx"))
        self.assertTrue(enricher._is_valid_filename("filing.tif"))
        
        # Invalid filenames
        self.assertFalse(enricher._is_valid_filename(""))
        self.assertFalse(enricher._is_valid_filename("no_extension"))
        self.assertFalse(enricher._is_valid_filename("document.xyz"))
        self.assertFalse(enricher._is_valid_filename("<html>document.pdf</html>"))
        self.assertFalse(enricher._is_valid_filename("a" * 300 + ".pdf"))  # Too long
    
    def test_parse_strategy_original_file(self):
        """Test parsing strategy for 'Original File' patterns."""
        enricher = EFileEnricher(self.config)
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.sample_html_responses["TEST001"], 'html.parser')
        
        filename = enricher._parse_strategy_original_file(soup)
        
        self.assertEqual(filename, "contract_agreement.pdf")
    
    def test_parse_strategy_css_selectors(self):
        """Test parsing strategy for CSS selectors."""
        enricher = EFileEnricher(self.config)
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.sample_html_responses["TEST002"], 'html.parser')
        
        filename = enricher._parse_strategy_css_selectors(soup)
        
        self.assertEqual(filename, "legal_document.docx")
    
    def test_parse_strategy_download_links(self):
        """Test parsing strategy for download links."""
        enricher = EFileEnricher(self.config)
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(self.sample_html_responses["TEST003"], 'html.parser')
        
        filename = enricher._parse_strategy_download_links(soup)
        
        self.assertEqual(filename, "filing.pdf")
    
    @patch('enhanced_efile_enricher.requests.Session.get')
    def test_fetch_page_with_retry_success(self, mock_get):
        """Test successful page fetching."""
        mock_response = Mock()
        mock_response.text = self.sample_html_responses["TEST001"]
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        enricher = EFileEnricher(self.config)
        
        html = enricher.fetch_page_with_retry("https://example.com/test", "TEST001")
        
        self.assertEqual(html, self.sample_html_responses["TEST001"])
        mock_get.assert_called_once()
    
    @patch('enhanced_efile_enricher.requests.Session.get')
    def test_fetch_page_with_retry_failure(self, mock_get):
        """Test page fetching with retries and failure."""
        mock_get.side_effect = Exception("Network error")
        
        enricher = EFileEnricher(self.config)
        
        html = enricher.fetch_page_with_retry("https://example.com/test", "TEST001")
        
        self.assertIsNone(html)
        self.assertEqual(mock_get.call_count, self.config.max_retries)
    
    @patch('enhanced_efile_enricher.EFileEnricher.fetch_page_with_retry')
    def test_get_original_filename_success(self, mock_fetch):
        """Test successful filename extraction."""
        mock_fetch.return_value = self.sample_html_responses["TEST001"]
        
        enricher = EFileEnricher(self.config)
        
        entry = self.sample_filings[0]
        filename = enricher.get_original_filename(entry)
        
        self.assertEqual(filename, "contract_agreement.pdf")
    
    def test_get_original_filename_skip_conditions(self):
        """Test skipping entries based on various conditions."""
        enricher = EFileEnricher(self.config)
        
        # Missing efile_id
        entry_no_id = {"efile_overview_url": "https://example.com/test"}
        self.assertIsNone(enricher.get_original_filename(entry_no_id))
        
        # Invalid URL
        entry_bad_url = {"efile_id": "TEST", "efile_overview_url": "not-a-url"}
        self.assertIsNone(enricher.get_original_filename(entry_bad_url))
        
        # Canceled status
        entry_canceled = {
            "efile_id": "TEST",
            "efile_overview_url": "https://example.com/test",
            "status": "canceled"
        }
        self.assertIsNone(enricher.get_original_filename(entry_canceled))
    
    @patch('enhanced_efile_enricher.EFileEnricher.get_original_filename')
    def test_process_filings(self, mock_get_filename):
        """Test processing multiple filings."""
        mock_get_filename.side_effect = ["file1.pdf", None, "file3.docx"]
        
        self.create_test_input_file()
        enricher = EFileEnricher(self.config)
        
        filings = enricher.load_filings()
        processed_filings = enricher.process_filings(filings)
        
        # Check that filenames were added where expected
        self.assertEqual(processed_filings[0].get("original_filename"), "file1.pdf")
        self.assertIsNone(processed_filings[2].get("original_filename"))  # Canceled, should be None
        
        # Check that already processed entry was skipped
        self.assertEqual(processed_filings[1].get("original_filename"), "already_processed.pdf")
    
    def test_save_filings(self):
        """Test saving enriched filings."""
        enricher = EFileEnricher(self.config)
        
        test_data = [{"test": "data"}]
        enricher.save_filings(test_data)
        
        # Check file was created
        self.assertTrue(Path(self.config.output_file).exists())
        
        # Check content
        with open(self.config.output_file, 'r') as f:
            saved_data = json.load(f)
        
        self.assertEqual(saved_data, test_data)

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete workflow."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.config = Config(
            input_file=os.path.join(self.test_dir, "integration_input.json"),
            output_file=os.path.join(self.test_dir, "integration_output.json"),
            cache_dir=os.path.join(self.test_dir, "cache"),
            log_file=os.path.join(self.test_dir, "integration.log"),
            request_delay=0.1,
            max_retries=1
        )
    
    def tearDown(self):
        """Clean up integration test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    @patch('enhanced_efile_enricher.requests.Session.get')
    def test_full_workflow(self, mock_get):
        """Test the complete enrichment workflow."""
        # Set up mock responses
        mock_response = Mock()
        mock_response.text = """
        <html>
            <body>
                <table>
                    <tr><td>Original File:</td><td>integration_test.pdf</td></tr>
                </table>
            </body>
        </html>
        """
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Create test input
        test_filings = [
            {
                "efile_id": "INTEGRATION001",
                "efile_overview_url": "https://example.com/filing/INTEGRATION001",
                "status": "completed"
            }
        ]
        
        with open(self.config.input_file, 'w') as f:
            json.dump(test_filings, f)
        
        # Run enricher
        enricher = EFileEnricher(self.config)
        success = enricher.run()
        
        # Verify success
        self.assertTrue(success)
        
        # Check output file
        self.assertTrue(Path(self.config.output_file).exists())
        
        with open(self.config.output_file, 'r') as f:
            result = json.load(f)
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["original_filename"], "integration_test.pdf")
        self.assertIn("enriched_timestamp", result[0])

if __name__ == '__main__':
    unittest.main()
